import { defineMessages } from 'react-intl';

export const profileFormMessages = defineMessages({
  editProfile: {
    id: 'profile.form.editProfile',
    defaultMessage: 'Edit Profile',
  },
  displayName: {
    id: 'profile.form.displayName',
    defaultMessage: 'Display Name',
  },
  enterYourDisplayName: {
    id: 'profile.form.enterYourDisplayName',
    defaultMessage: 'Enter your display name',
  },
  updating: {
    id: 'profile.form.updating',
    defaultMessage: 'Updating...',
  },
  updateProfile: {
    id: 'profile.form.updateProfile',
    defaultMessage: 'Update Profile',
  },
  profileUpdatedSuccessfully: {
    id: 'profile.form.profileUpdatedSuccessfully',
    defaultMessage: 'Profile updated successfully!',
  },
  failedToUpdateProfile: {
    id: 'profile.form.failedToUpdateProfile',
    defaultMessage: 'Failed to update profile. Please try again.',
  },
  nameIsRequired: {
    id: 'profile.form.nameIsRequired',
    defaultMessage: 'Name is required',
  },
  nameTooLong: {
    id: 'profile.form.nameTooLong',
    defaultMessage: 'Name must be less than 50 characters',
  },
});
